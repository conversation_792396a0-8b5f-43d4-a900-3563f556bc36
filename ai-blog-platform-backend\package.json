{"name": "ai-blog-platform-backend", "version": "1.0.0", "description": "AI-driven blog content generation platform backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js", "sync-sheets": "node scripts/syncGoogleSheets.js", "test:watch": "jest --watch", "cleanup": "node scripts/cleanupDatabase.js", "test-db": "node scripts/testConnection.js", "test-apis": "node scripts/testAPIs.js", "quick-start": "node scripts/quickStart.js"}, "keywords": ["ai", "blog", "content-generation", "gemini", "mongodb", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cheerio": "^1.1.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^6.11.2", "google-auth-library": "^9.15.1", "google-spreadsheet": "^4.1.5", "helmet": "^7.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "mongodb": "4.1", "mongoose": "^7.8.7", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "sharp": "^0.32.6", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.14", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}