// routes/blogRoutes.js
const express = require('express');
const BlogData = require('../models/BlogData');
const ContentBlock = require('../models/ContentBlock');
const router = express.Router();

// GET all blogs with pagination
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const filter = {};
    if (req.query.status) filter.status = req.query.status;
    if (req.query.companyId) filter.companyId = req.query.companyId;
    
    const blogs = await BlogData.find(filter)
      .populate('companyId', 'name tone brandVoice')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await BlogData.countDocuments(filter);
    
    res.json({
      blogs,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET blog by ID with content blocks
router.get('/:id', async (req, res) => {
  try {
    const blog = await BlogData.findById(req.params.id)
      .populate('companyId');
    
    if (!blog) {
      return res.status(404).json({ message: 'Blog not found' });
    }
    
    const contentBlocks = await ContentBlock.find({ blogId: req.params.id })
      .sort({ order: 1 });
    
    res.json({ blog, contentBlocks });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST create new blog
router.post('/', async (req, res) => {
  try {
    const blog = new BlogData(req.body);
    const savedBlog = await blog.save();
    res.status(201).json(savedBlog);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// PUT update blog status
router.put('/:id/status', async (req, res) => {
  try {
    const { status } = req.body;
    const blog = await BlogData.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true }
    );
    if (!blog) {
      return res.status(404).json({ message: 'Blog not found' });
    }
    res.json(blog);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;