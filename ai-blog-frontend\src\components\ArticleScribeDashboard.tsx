'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, FileText, Plus } from 'lucide-react';

const ArticleScribeDashboard = () => {
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [recentDrafts, setRecentDrafts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCompanies();
    fetchRecentDrafts();
  }, []);

  const fetchCompanies = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/companies');
      if (response.ok) {
        const data = await response.json();
        setCompanies(data);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    }
  };

  const fetchRecentDrafts = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/blogs');
      if (response.ok) {
        const data = await response.json();
        setRecentDrafts(data.blogs || []);
      }
    } catch (error) {
      console.error('Error fetching drafts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartNewBlog = () => {
    if (!selectedCompany) {
      alert('Please select a company first');
      return;
    }
    // Navigate to blog creation page
    window.location.href = `/create-blog?company=${selectedCompany}`;
  };

  const selectedCompanyData = companies.find(c => c._id === selectedCompany);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">ArticleScribe</h1>
            <p className="text-gray-600 mt-1">AI Blog Builder with WordPress Deployment</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Select Company Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-blue-100 p-2 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Select Company</h2>
                <p className="text-gray-600">Choose a company to start creating your AI-powered blog post</p>
              </div>
            </div>

            {/* Company Dropdown */}
            <div className="space-y-4">
              <div className="relative">
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className={selectedCompany ? 'text-gray-900' : 'text-gray-500'}>
                      {selectedCompanyData ? selectedCompanyData.name : 'Select a company...'}
                    </span>
                    <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                  </div>
                </button>

                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                    <div className="py-1">
                      {companies.length === 0 ? (
                        <div className="px-4 py-3 text-gray-500 text-sm">No companies found</div>
                      ) : (
                        companies.map((company) => (
                          <button
                            key={company._id}
                            onClick={() => {
                              setSelectedCompany(company._id);
                              setIsDropdownOpen(false);
                            }}
                            className="w-full text-left px-4 py-3 text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors"
                          >
                            <div>
                              <div className="font-medium">{company.name}</div>
                              <div className="text-sm text-gray-500 truncate">{company.serviceOverview}</div>
                            </div>
                          </button>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Start New Blog Button */}
              <button
                onClick={handleStartNewBlog}
                disabled={!selectedCompany}
                className={`w-full py-4 px-6 rounded-lg text-white font-medium text-lg transition-all duration-200 ${
                  selectedCompany
                    ? 'bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm hover:shadow-md'
                    : 'bg-gray-300 cursor-not-allowed'
                }`}
              >
                Start New Blog
              </button>
            </div>
          </div>

          {/* Recent Drafts Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-gray-100 p-2 rounded-lg">
                <FileText className="h-6 w-6 text-gray-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Recent Drafts</h2>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading drafts...</p>
              </div>
            ) : recentDrafts.length === 0 ? (
              <div className="text-center py-12">
                <div className="bg-gray-100 rounded-full p-3 w-16 h-16 mx-auto mb-4">
                  <FileText className="h-10 w-10 text-gray-400 mx-auto" />
                </div>
                <p className="text-gray-500 text-lg">Start your first blog post!</p>
                <p className="text-gray-400 text-sm mt-1">Select a company and click "Start New Blog" to begin</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentDrafts.slice(0, 5).map((draft) => (
                  <div
                    key={draft._id}
                    className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors cursor-pointer"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 mb-1">
                          {draft.focusKeyword}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {draft.articleFormat} • {draft.wordCount} words • {draft.targetAudience}
                        </p>
                        <div className="flex items-center space-x-4">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            draft.status === 'completed' ? 'bg-green-100 text-green-800' :
                            draft.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {draft.status.replace('-', ' ')}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(draft.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Continue
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleScribeDashboard;