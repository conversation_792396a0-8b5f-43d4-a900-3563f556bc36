// services/googleSheetsService.js
const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');

class GoogleSheetsService {
  constructor() {
    this.serviceAccountAuth = new JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      scopes: ['https://www.googleapis.com/auth/spreadsheets']
    });
  }

  async syncBlogDataSheet(spreadsheetId, sheetTitle = 'WattMonk Blog Data') {
    try {
      const doc = new GoogleSpreadsheet(spreadsheetId, this.serviceAccountAuth);
      await doc.loadInfo();
      
      const sheet = doc.sheetsByTitle[sheetTitle];
      if (!sheet) {
        throw new Error(`Sheet "${sheetTitle}" not found`);
      }

      const rows = await sheet.getRows();
      const blogData = rows.map(row => ({
        focusKeyword: row.get('Focus Keyword') || row.get('focus_keyword'),
        articleFormat: row.get('Article Format') || row.get('article_format'),
        wordCount: parseInt(row.get('Word Count') || row.get('word_count')) || 1000,
        targetAudience: row.get('Target Audience') || row.get('target_audience'),
        objective: row.get('Objective') || row.get('objective'),
        priority: parseInt(row.get('Priority')) || 1,
        status: row.get('Status')?.toLowerCase() || 'pending'
      })).filter(item => item.focusKeyword);

      return blogData;
    } catch (error) {
      console.error('Google Sheets sync error:', error);
      throw error;
    }
  }

  async syncCompanyDataSheet(spreadsheetId, sheetTitle = 'Company KT Sheet') {
    try {
      const doc = new GoogleSpreadsheet(spreadsheetId, this.serviceAccountAuth);
      await doc.loadInfo();
      
      const sheet = doc.sheetsByTitle[sheetTitle];
      if (!sheet) {
        throw new Error(`Sheet "${sheetTitle}" not found`);
      }

      const rows = await sheet.getRows();
      const companyData = rows.map(row => ({
        name: row.get('Company Name') || row.get('company_name'),
        servicesOffered: (row.get('Services Offered') || row.get('services_offered') || '')
          .split(',')
          .map(service => ({
            name: service.trim(),
            description: ''
          })),
        serviceOverview: row.get('Service Overview') || row.get('service_overview'),
        aboutCompany: row.get('About The Company') || row.get('about_company'),
        tone: (row.get('Tone') || row.get('tone') || 'professional').toLowerCase(),
        brandVoice: row.get('Brand Voice') || row.get('brand_voice'),
        targetAudience: (row.get('Target Audience') || row.get('target_audience') || '')
          .split(',')
          .map(audience => audience.trim())
          .filter(audience => audience.length > 0)
      })).filter(item => item.name);

      return companyData;
    } catch (error) {
      console.error('Company data sync error:', error);
      throw error;
    }
  }
}

module.exports = new GoogleSheetsService();